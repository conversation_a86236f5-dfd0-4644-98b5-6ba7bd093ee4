#include <stdint.h>
#include <stdio.h>

// Estrutura para representar um número de 256 bits
typedef struct {
    uint32_t d[8];  // 256 bits = 8 * 32 bits
} uint256_t;

// Estrutura para representar um ponto em coordenadas jacobianas
typedef struct {
    uint256_t x, y, z;
    int infinity;  // flag para ponto no infinito
} point_jacobian_t;

// Estrutura para representar um ponto em coordenadas afins
typedef struct {
    uint256_t x, y;
} point_affine_t;

// Function declarations
__device__ void uint256_zero(uint256_t* a);
__device__ void uint256_copy(uint256_t* dst, const uint256_t* src);
__device__ void uint256_from_uint64(uint256_t* a, uint64_t val);
__device__ int uint256_is_zero(const uint256_t* a);
__device__ int uint256_cmp(const uint256_t* a, const uint256_t* b);
__device__ void uint256_addmod(uint256_t* r, const uint256_t* a, const uint256_t* b, const uint256_t* mod);
__device__ void uint256_submod(uint256_t* r, const uint256_t* a, const uint256_t* b, const uint256_t* mod);
__device__ void uint256_mulmod(uint256_t* r, const uint256_t* a, const uint256_t* b, const uint256_t* mod);
__device__ void point_double_jacobian(point_jacobian_t* r, const point_jacobian_t* p);
__device__ void point_add_jacobian(point_jacobian_t* r, const point_jacobian_t* p1, const point_jacobian_t* p2);

// Ponto gerador G secp256k1 (coordenadas corretas)
__constant__ uint32_t GX[8] = {
    0x79BE667E, 0xF9DCBBAC, 0x55A06295, 0xCE870B07,
    0x029BFCDB, 0x2DCE28D9, 0x59F2815B, 0x16F81798
};
    
__constant__ uint32_t GY[8] = {
    0x483ADA77, 0x26A3C465, 0x5DA4FBFC, 0x0E1108A8,
    0xFD17B448, 0xA6855419, 0x9C47D08F, 0xFB10D4B8
};

__constant__ uint256_t SECP256K1_GX = {{0x79BE667E, 0xF9DCBBAC, 0x55A06295, 0xCE870B07,
                                       0x029BFCDB, 0x2DCE28D9, 0x59F2815B, 0x16F81798}};
    
__constant__ uint256_t SECP256K1_GY = {{0x483ADA77, 0x26A3C465, 0x5DA4FBFC, 0x0E1108A8,
                                       0xFD17B448, 0xA6855419, 0x9C47D08F, 0xFB10D4B8}};
    
// Primo da curva secp256k1: p = 2^256 - 2^32 - 977
__constant__ uint256_t SECP256K1_P = {{0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
                                     0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFE, 0xFFFFFC2F}};

// Operações básicas uint256
__device__ __forceinline__ void uint256_zero(uint256_t* a) {
    for (int i = 0; i < 8; i++) a->d[i] = 0;
}

__device__ __forceinline__ void uint256_copy(uint256_t* dst, const uint256_t* src) {
    for (int i = 0; i < 8; i++) dst->d[i] = src->d[i];
}

__device__ __forceinline__ void uint256_from_uint64(uint256_t* a, uint64_t val) {
    uint256_zero(a);
    a->d[7] = (uint32_t)(val & 0xFFFFFFFF);
    a->d[6] = (uint32_t)(val >> 32);
}

__device__ __forceinline__ int uint256_is_zero(const uint256_t* a) {
    for (int i = 0; i < 8; i++) {
        if (a->d[i] != 0) return 0;
    }
    return 1;
}

__device__ __forceinline__ int uint256_cmp(const uint256_t* a, const uint256_t* b) {
    for (int i = 0; i < 8; i++) {
        if (a->d[i] > b->d[i]) return 1;
        if (a->d[i] < b->d[i]) return -1;
    }
    return 0;
}

// Adição modular
__device__ __forceinline__ void uint256_addmod(uint256_t* r, const uint256_t* a, const uint256_t* b, const uint256_t* mod) {
    uint64_t carry = 0;
    uint256_t temp;
    
    for (int i = 7; i >= 0; i--) {
        carry += (uint64_t)a->d[i] + b->d[i];
        temp.d[i] = (uint32_t)carry;
        carry >>= 32;
    }
    
    // Redução modular se necessário
    if (carry || uint256_cmp(&temp, mod) >= 0) {
        carry = 0;
        for (int i = 7; i >= 0; i--) {
            carry += (uint64_t)temp.d[i] - mod->d[i];
            temp.d[i] = (uint32_t)carry;
            carry = (carry >> 32) ? 0xFFFFFFFFFFFFFFFF : 0;
        }
    }
    
    uint256_copy(r, &temp);
}

// Subtração modular
__device__ __forceinline__ void uint256_submod(uint256_t* r, const uint256_t* a, const uint256_t* b, const uint256_t* mod) {
    uint256_t temp;
    if (uint256_cmp(a, b) >= 0) {
        uint64_t borrow = 0;
        for (int i = 7; i >= 0; i--) {
            borrow += (uint64_t)a->d[i] - b->d[i];
            temp.d[i] = (uint32_t)borrow;
            borrow = (borrow >> 32) ? 0xFFFFFFFFFFFFFFFF : 0;
        }
    } else {
        // a < b, então resultado = mod - b + a
        uint64_t borrow = 0;
        for (int i = 7; i >= 0; i--) {
            borrow += (uint64_t)mod->d[i] - b->d[i] + a->d[i];
            temp.d[i] = (uint32_t)borrow;
            borrow = (borrow >> 32) ? 0xFFFFFFFFFFFFFFFF : 0;
        }
    }
    uint256_copy(r, &temp);
}

// Multiplicação modular
__device__ __forceinline__ void uint256_mulmod(uint256_t* r, const uint256_t* a, const uint256_t* b, const uint256_t* mod) {
    uint256_t result;
    uint256_zero(&result);
    uint256_t temp_a;
    uint256_copy(&temp_a, a);
    
    for (int i = 7; i >= 0; i--) {
        unsigned int mask = 1;
        for (int j = 0; j < 32; j++) {
            if (b->d[i] & mask) {
                uint256_addmod(&result, &result, &temp_a, mod);
            }
            mask = mask << 1;
            uint256_addmod(&temp_a, &temp_a, &temp_a, mod); // temp_a *= 2
        }
    }
    
    uint256_copy(r, &result);
}

// Funções de ponto da curva elíptica
__device__ void point_double_jacobian(point_jacobian_t* r, const point_jacobian_t* p) {
    if (p->infinity) {
        *r = *p;
        return;
    }
    
    uint256_t s, m, x3, y3, z3, temp1, temp2;
    
    // s = 4*x*y^2
    uint256_mulmod(&temp1, &p->y, &p->y, &SECP256K1_P);      // y^2
    uint256_mulmod(&s, &p->x, &temp1, &SECP256K1_P);         // x*y^2
    uint256_addmod(&s, &s, &s, &SECP256K1_P);                // 2*x*y^2
    uint256_addmod(&s, &s, &s, &SECP256K1_P);                // 4*x*y^2
    
    // m = 3*x^2 (para secp256k1, a=0)
    uint256_mulmod(&m, &p->x, &p->x, &SECP256K1_P);          // x^2
    uint256_addmod(&temp1, &m, &m, &SECP256K1_P);            // 2*x^2
    uint256_addmod(&m, &temp1, &m, &SECP256K1_P);            // 3*x^2
    
    // x3 = m^2 - 2*s
    uint256_mulmod(&x3, &m, &m, &SECP256K1_P);               // m^2
    uint256_submod(&x3, &x3, &s, &SECP256K1_P);              // m^2 - s
    uint256_submod(&x3, &x3, &s, &SECP256K1_P);              // m^2 - 2*s
    
    // y3 = m*(s - x3) - 8*y^4
    uint256_submod(&temp1, &s, &x3, &SECP256K1_P);           // s - x3
    uint256_mulmod(&y3, &m, &temp1, &SECP256K1_P);           // m*(s - x3)
    uint256_mulmod(&temp1, &p->y, &p->y, &SECP256K1_P);      // y^2
    uint256_mulmod(&temp2, &temp1, &temp1, &SECP256K1_P);    // y^4
    uint256_addmod(&temp1, &temp2, &temp2, &SECP256K1_P);    // 2*y^4
    uint256_addmod(&temp1, &temp1, &temp1, &SECP256K1_P);    // 4*y^4
    uint256_addmod(&temp1, &temp1, &temp1, &SECP256K1_P);    // 8*y^4
    uint256_submod(&y3, &y3, &temp1, &SECP256K1_P);          // m*(s-x3) - 8*y^4
    
    // z3 = 2*y*z
    uint256_mulmod(&z3, &p->y, &p->z, &SECP256K1_P);         // y*z
    uint256_addmod(&z3, &z3, &z3, &SECP256K1_P);             // 2*y*z
    
    uint256_copy(&r->x, &x3);
    uint256_copy(&r->y, &y3);
    uint256_copy(&r->z, &z3);
    r->infinity = 0;
}

__device__ void point_add_jacobian(point_jacobian_t* r, const point_jacobian_t* p1, const point_jacobian_t* p2) {
    if (p1->infinity) {
        *r = *p2;
        return;
    }
    if (p2->infinity) {
        *r = *p1;
        return;
    }
    
    // Implementação simplificada - assumindo z2 = 1 (ponto afim)
    uint256_t u1, u2, s1, s2, h, r_val, x3, y3, z3, temp1, temp2;
    
    // u1 = x1, u2 = x2*z1^2
    uint256_copy(&u1, &p1->x);
    uint256_mulmod(&temp1, &p1->z, &p1->z, &SECP256K1_P);    // z1^2
    uint256_mulmod(&u2, &p2->x, &temp1, &SECP256K1_P);       // x2*z1^2
    
    // s1 = y1, s2 = y2*z1^3
    uint256_copy(&s1, &p1->y);
    uint256_mulmod(&temp2, &temp1, &p1->z, &SECP256K1_P);    // z1^3
    uint256_mulmod(&s2, &p2->y, &temp2, &SECP256K1_P);       // y2*z1^3
    
    // h = u2 - u1
    uint256_submod(&h, &u2, &u1, &SECP256K1_P);
    
    // r = s2 - s1
    uint256_submod(&r_val, &s2, &s1, &SECP256K1_P);
    
    // Se h = 0, pontos têm mesma coordenada x
    if (uint256_is_zero(&h)) {
        if (uint256_is_zero(&r_val)) {
            // Pontos são iguais, fazer duplicação
            point_double_jacobian(r, p1);
            return;
        } else {
            // Pontos são opostos, resultado é infinito
            r->infinity = 1;
            return;
        }
    }
    
    // x3 = r^2 - h^3 - 2*u1*h^2
    uint256_mulmod(&temp1, &r_val, &r_val, &SECP256K1_P);    // r^2
    uint256_mulmod(&temp2, &h, &h, &SECP256K1_P);            // h^2
    uint256_mulmod(&x3, &h, &temp2, &SECP256K1_P);           // h^3
    uint256_submod(&x3, &temp1, &x3, &SECP256K1_P);          // r^2 - h^3
    uint256_mulmod(&temp1, &u1, &temp2, &SECP256K1_P);       // u1*h^2
    uint256_submod(&x3, &x3, &temp1, &SECP256K1_P);          // r^2 - h^3 - u1*h^2
    uint256_submod(&x3, &x3, &temp1, &SECP256K1_P);          // r^2 - h^3 - 2*u1*h^2
    
    // y3 = r*(u1*h^2 - x3) - s1*h^3
    uint256_submod(&temp1, &temp1, &x3, &SECP256K1_P);       // u1*h^2 - x3
    uint256_mulmod(&y3, &r_val, &temp1, &SECP256K1_P);       // r*(u1*h^2 - x3)
    uint256_mulmod(&temp1, &h, &temp2, &SECP256K1_P);        // h^3
    uint256_mulmod(&temp2, &s1, &temp1, &SECP256K1_P);       // s1*h^3
    uint256_submod(&y3, &y3, &temp2, &SECP256K1_P);          // r*(u1*h^2 - x3) - s1*h^3
    
    // z3 = z1*h
    uint256_mulmod(&z3, &p1->z, &h, &SECP256K1_P);
    
    uint256_copy(&r->x, &x3);
    uint256_copy(&r->y, &y3);
    uint256_copy(&r->z, &z3);
    r->infinity = 0;
}

__device__ void point_multiply_scalar(point_jacobian_t* result, uint64_t scalar) {
    // Inicializar com ponto no infinito
    result->infinity = 1;
    uint256_zero(&result->x);
    uint256_zero(&result->y);
    uint256_zero(&result->z);
    
    if (scalar == 0) return;
    
    // Inicializar o ponto base G
    point_jacobian_t base;
    base.infinity = 0;
    uint256_from_uint64(&base.z, 1);
    for (int i = 0; i < 8; i++) {
        base.x.d[i] = SECP256K1_GX.d[i];
        base.y.d[i] = SECP256K1_GY.d[i];
    }
    uint256_from_uint64(&base.z, 1);  // z = 1
    base.infinity = 0;
    
    // Double-and-add algorithm
    for (int i = 63; i >= 0; i--) {
        if (!result->infinity) {
            point_double_jacobian(result, result);
        }
        
        if (scalar & (1ULL << i)) {
            if (result->infinity) {
                *result = base;
            } else {
                point_add_jacobian(result, result, &base);
            }
        }
    }
}

// Conversão de Jacobiano para Afim
__device__ void jacobian_to_affine(point_jacobian_t* point) {
    if (point->infinity) return;
    
    // Para simplificar, assumir z = 1 (já em coordenadas afins)
    // Em uma implementação completa, seria necessário calcular z^(-1)
}

// Converter ponto para chave pública comprimida
__device__ void point_to_compressed_pubkey(const point_jacobian_t* point, uint8_t* pubkey) {
    if (point->infinity) {
        for (int i = 0; i < 33; i++) pubkey[i] = 0;
        return;
    }
    
    // Byte de prefixo (02 se y é par, 03 se y é ímpar)
    pubkey[0] = (point->y.d[7] & 1) ? 0x03 : 0x02;
    
    // Coordenada x (32 bytes, big-endian)
    for (int i = 0; i < 8; i++) {
        uint32_t word = point->x.d[i];
        pubkey[1 + i*4] = (word >> 24) & 0xFF;
        pubkey[1 + i*4 + 1] = (word >> 16) & 0xFF;
        pubkey[1 + i*4 + 2] = (word >> 8) & 0xFF;
        pubkey[1 + i*4 + 3] = word & 0xFF;
    }
}

// SHA-256 simplificado (para teste)
__device__ void sha256_simple(const uint8_t* input, uint32_t len, uint8_t* output) {
    // Implementação muito simplificada para teste
    for (int i = 0; i < 32; i++) {
        output[i] = 0;
        for (int j = 0; j < len; j++) {
            output[i] ^= input[j] + i + j;
        }
        output[i] ^= (i * 0x5A);
    }
}

// RIPEMD-160 simplificado (para teste)
__device__ void ripemd160_simple(const uint8_t* input, uint8_t* output) {
    for (int i = 0; i < 20; i++) {
        output[i] = input[i] ^ input[i + 12] ^ (i * 0x3C);
    }
}

// Kernel de teste para uma única chave
__global__ void test_single_key_correct(uint64_t key, uint8_t* hash160_result, uint8_t* pubkey_result, uint8_t* sha256_result) {
    if (threadIdx.x == 0 && blockIdx.x == 0) {
        printf("GPU: Testando chave %llu\n", key);
        
        // Multiplicação escalar: key * G
        point_jacobian_t pubkey_point;
        point_multiply_scalar(&pubkey_point, key);
        
        printf("GPU: Multiplicação escalar concluída\n");
        
        // Converter para coordenadas afins
        jacobian_to_affine(&pubkey_point);
        
        // Debug: mostrar coordenadas
        printf("GPU: Ponto X: ");
        for (int i = 0; i < 8; i++) {
            printf("%08x", pubkey_point.x.d[i]);
        }
        printf("\n");
        
        printf("GPU: Ponto Y: ");
        for (int i = 0; i < 8; i++) {
            printf("%08x", pubkey_point.y.d[i]);
        }
        printf("\n");
        
        // Converter para chave pública comprimida
        point_to_compressed_pubkey(&pubkey_point, pubkey_result);
        
        printf("GPU: Chave pública: ");
        for (int i = 0; i < 33; i++) {
            printf("%02x", pubkey_result[i]);
        }
        printf("\n");
        
        // SHA-256
        sha256_simple(pubkey_result, 33, sha256_result);
        
        printf("GPU: SHA-256: ");
        for (int i = 0; i < 32; i++) {
            printf("%02x", sha256_result[i]);
        }
        printf("\n");
        
        // RIPEMD-160
        ripemd160_simple(sha256_result, hash160_result);
        
        printf("GPU: Hash160: ");
        for (int i = 0; i < 20; i++) {
            printf("%02x", hash160_result[i]);
        }
        printf("\n");
    }
}

// Kernel para testar com chave de 256 bits
__global__ void test_key_256(const uint32_t* key_words, uint8_t* hash160_result, uint8_t* pubkey_result, uint8_t* sha256_result) {
    if (threadIdx.x == 0 && blockIdx.x == 0) {
        // Construir chave de 256 bits
        uint256_t private_key;
        for (int i = 0; i < 8; i++) {
            private_key.d[i] = key_words[i];
        }
        
        printf("GPU: Chave privada 256-bit: ");
        for (int i = 0; i < 8; i++) {
            printf("%08x", private_key.d[i]);
        }
        printf("\n");
        
        // Multiplicação escalar: key * G
        point_jacobian_t pubkey_point;
        point_multiply_scalar_256(&pubkey_point, &private_key);
        
        // Converter para coordenadas afins
        jacobian_to_affine(&pubkey_point);
        
        // Converter para chave pública comprimida
        point_to_compressed_pubkey(&pubkey_point, pubkey_result);
        
        printf("GPU: Chave pública: ");
        for (int i = 0; i < 33; i++) {
            printf("%02x", pubkey_result[i]);
        }
        printf("\n");
        
        // SHA-256 e RIPEMD-160
        sha256_simple(pubkey_result, 33, sha256_result);
        ripemd160_simple(sha256_result, hash160_result);
        
        printf("GPU: Hash160: ");
        for (int i = 0; i < 20; i++) {
            printf("%02x", hash160_result[i]);
        }
        printf("\n");
    }
}

