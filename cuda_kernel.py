#!/usr/bin/env python3
import os
"""
Kernel CUDA OTIMIZADO para conversão chave privada → hash160
Implementação completa de secp256k1, SHA-256 e RIPEMD-160
Alto desempenho para busca massiva de Bitcoin
"""

import numpy as np
from pycuda import driver as cuda
from pycuda.compiler import SourceModule
import pycuda.autoinit
import hashlib
import ecdsa
from Crypto.Hash import RIPEMD160

def create_optimized_cuda_kernel():
    """Cria kernel CUDA otimizado para máximo throughput"""
    # Read the CUDA kernel from the external file
    kernel_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'bitcoin_kernel.cu')
    try:
        with open(kernel_path, 'r') as f:
            cuda_code = f.read()
        return cuda_code
    except Exception as e:
        print(f"Error reading CUDA kernel file: {e}")
        raise
        
def create_correct_cuda_kernel():
    """Cria kernel CUDA com implementação ECDSA completa e correta"""
    cuda_code = """
    #include <stdint.h>
    #include <stdio.h>
    
    // Ponto gerador G secp256k1 (coordenadas corretas)
    __constant__ uint32_t GX[8] = {
        0x79BE667E, 0xF9DCBBAC, 0x55A06295, 0xCE870B07,
        0x029BFCDB, 0x2DCE28D9, 0x59F2815B, 0x16F81798
    };
    
    __constant__ uint32_t GY[8] = {
        0x483ADA77, 0x26A3C465, 0x5DA4FBFC, 0x0E1108A8,
        0xFD17B448, 0xA6855419, 0x9C47D08F, 0xFB10D4B8
    };
    
    // Primo da curva secp256k1: p = 2^256 - 2^32 - 977
    __constant__ uint32_t SECP256K1_P[8] = {
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFE, 0xFFFFFC2F
    };
    
    typedef struct {
        uint32_t d[8];  // 256 bits = 8 * 32 bits
    } uint256_t;
    
    typedef struct {
        uint256_t x, y, z;  // Coordenadas Jacobianas
        int infinity;
    } point_jacobian_t;
    
    // Operações básicas uint256
    __device__ __forceinline__ void uint256_zero(uint256_t* a) {
        for (int i = 0; i < 8; i++) a->d[i] = 0;
    }
    
    __device__ __forceinline__ void uint256_copy(uint256_t* dst, const uint256_t* src) {
        for (int i = 0; i < 8; i++) dst->d[i] = src->d[i];
    }
    
    __device__ __forceinline__ void uint256_from_uint64(uint256_t* a, uint64_t val) {
        uint256_zero(a);
        a->d[7] = (uint32_t)(val & 0xFFFFFFFF);
        a->d[6] = (uint32_t)(val >> 32);
    }
    
    __device__ __forceinline__ void uint256_from_array(uint256_t* a, const uint32_t* arr) {
        for (int i = 0; i < 8; i++) a->d[i] = arr[i];
    }
    
    __device__ __forceinline__ int uint256_is_zero(const uint256_t* a) {
        for (int i = 0; i < 8; i++) {
            if (a->d[i] != 0) return 0;
        }
        return 1;
    }
    
    __device__ __forceinline__ int uint256_cmp(const uint256_t* a, const uint256_t* b) {
        for (int i = 0; i < 8; i++) {
            if (a->d[i] > b->d[i]) return 1;
            if (a->d[i] < b->d[i]) return -1;
        }
        return 0;
    }
    
    // Adição com carry
    __device__ __forceinline__ int uint256_add(uint256_t* r, const uint256_t* a, const uint256_t* b) {
        uint64_t carry = 0;
        for (int i = 7; i >= 0; i--) {
            carry += (uint64_t)a->d[i] + b->d[i];
            r->d[i] = (uint32_t)carry;
            carry >>= 32;
        }
        return carry != 0;
    }
    
    // Subtração com borrow
    __device__ __forceinline__ int uint256_sub(uint256_t* r, const uint256_t* a, const uint256_t* b) {
        uint64_t borrow = 0;
        for (int i = 7; i >= 0; i--) {
            borrow = (uint64_t)a->d[i] - b->d[i] - borrow;
            r->d[i] = (uint32_t)borrow;
            borrow = (borrow >> 32) & 1;
        }
        return borrow != 0;
    }
    
    // Adição modular para secp256k1
    __device__ __forceinline__ void uint256_addmod_secp256k1(uint256_t* r, const uint256_t* a, const uint256_t* b) {
        uint256_t temp;
        uint256_t p;
        uint256_from_array(&p, SECP256K1_P);
        
        int overflow = uint256_add(&temp, a, b);
        
        if (overflow || uint256_cmp(&temp, &p) >= 0) {
            uint256_sub(r, &temp, &p);
        } else {
            uint256_copy(r, &temp);
        }
    }
    
    // Subtração modular para secp256k1
    __device__ __forceinline__ void uint256_submod_secp256k1(uint256_t* r, const uint256_t* a, const uint256_t* b) {
        uint256_t p;
        uint256_from_array(&p, SECP256K1_P);
        
        if (uint256_cmp(a, b) >= 0) {
            uint256_sub(r, a, b);
        } else {
            uint256_t temp;
            uint256_sub(&temp, &p, b);
            uint256_addmod_secp256k1(r, &temp, a);
        }
    }
    
    // Multiplicação modular usando Montgomery para secp256k1
    __device__ void uint256_mulmod_secp256k1(uint256_t* r, const uint256_t* a, const uint256_t* b) {
        uint256_t result;
        uint256_zero(&result);
        uint256_t temp_a;
        uint256_copy(&temp_a, a);
        
        for (int i = 7; i >= 0; i--) {
            uint32_t word = b->d[i];
            for (int j = 0; j < 32; j++) {
                if (word & (1U << j)) {
                    uint256_addmod_secp256k1(&result, &result, &temp_a);
                }
                uint256_addmod_secp256k1(&temp_a, &temp_a, &temp_a);
            }
        }
        
        uint256_copy(r, &result);
    }
    
    // Exponenciação modular (para inversão)
    __device__ void uint256_powmod_secp256k1(uint256_t* r, const uint256_t* base, const uint256_t* exp) {
        uint256_t result;
        uint256_from_uint64(&result, 1);
        uint256_t temp_base;
        uint256_copy(&temp_base, base);
        
        for (int i = 7; i >= 0; i--) {
            uint32_t word = exp->d[i];
            for (int j = 0; j < 32; j++) {
                if (word & (1U << j)) {
                    uint256_mulmod_secp256k1(&result, &result, &temp_base);
                }
                uint256_mulmod_secp256k1(&temp_base, &temp_base, &temp_base);
            }
        }
        
        uint256_copy(r, &result);
    }
    
    // Inversão modular usando Fermat's little theorem: a^(p-2) mod p
    __device__ void uint256_invmod_secp256k1(uint256_t* r, const uint256_t* a) {
        uint256_t p_minus_2;
        uint256_from_array(&p_minus_2, SECP256K1_P);
        
        // p - 2 = FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEFFFFFC2D
        uint256_t two;
        uint256_from_uint64(&two, 2);
        uint256_submod_secp256k1(&p_minus_2, &p_minus_2, &two);
        
        uint256_powmod_secp256k1(r, a, &p_minus_2);
    }
    
    // Duplicação de ponto em coordenadas Jacobianas
    __device__ void point_double_jacobian(point_jacobian_t* r, const point_jacobian_t* p) {
        if (p->infinity) {
            *r = *p;
            return;
        }
        
        uint256_t s, m, x3, y3, z3, temp1, temp2, temp3;
        
        // s = 4*x*y^2
        uint256_mulmod_secp256k1(&temp1, &p->y, &p->y);      // y^2
        uint256_mulmod_secp256k1(&s, &p->x, &temp1);         // x*y^2
        uint256_addmod_secp256k1(&s, &s, &s);                // 2*x*y^2
        uint256_addmod_secp256k1(&s, &s, &s);                // 4*x*y^2
        
        // m = 3*x^2 (para secp256k1, a=0)
        uint256_mulmod_secp256k1(&m, &p->x, &p->x);          // x^2
        uint256_addmod_secp256k1(&temp1, &m, &m);            // 2*x^2
        uint256_addmod_secp256k1(&m, &temp1, &m);            // 3*x^2
        
        // x3 = m^2 - 2*s
        uint256_mulmod_secp256k1(&x3, &m, &m);               // m^2
        uint256_submod_secp256k1(&x3, &x3, &s);              // m^2 - s
        uint256_submod_secp256k1(&x3, &x3, &s);              // m^2 - 2*s
        
        // y3 = m*(s - x3) - 8*y^4
        uint256_submod_secp256k1(&temp1, &s, &x3);           // s - x3
        uint256_mulmod_secp256k1(&y3, &m, &temp1);           // m*(s - x3)
        uint256_mulmod_secp256k1(&temp1, &p->y, &p->y);      // y^2
        uint256_mulmod_secp256k1(&temp2, &temp1, &temp1);    // y^4
        uint256_addmod_secp256k1(&temp1, &temp2, &temp2);    // 2*y^4
        uint256_addmod_secp256k1(&temp1, &temp1, &temp1);    // 4*y^4
        uint256_addmod_secp256k1(&temp1, &temp1, &temp1);    // 8*y^4
        uint256_submod_secp256k1(&y3, &y3, &temp1);          // m*(s-x3) - 8*y^4
        
        // z3 = 2*y*z
        uint256_mulmod_secp256k1(&z3, &p->y, &p->z);         // y*z
        uint256_addmod_secp256k1(&z3, &z3, &z3);             // 2*y*z
        
        uint256_copy(&r->x, &x3);
        uint256_copy(&r->y, &y3);
        uint256_copy(&r->z, &z3);
        r->infinity = 0;
    }
    
    // Adição de pontos em coordenadas Jacobianas
    __device__ void point_add_jacobian(point_jacobian_t* r, const point_jacobian_t* p1, const point_jacobian_t* p2) {
        if (p1->infinity) {
            *r = *p2;
            return;
        }
        if (p2->infinity) {
            *r = *p1;
            return;
        }
        
        uint256_t u1, u2, s1, s2, h, r_val, x3, y3, z3, temp1, temp2, temp3;
        
        // u1 = x1 * z2^2, u2 = x2 * z1^2
        uint256_mulmod_secp256k1(&temp1, &p2->z, &p2->z);    // z2^2
        uint256_mulmod_secp256k1(&u1, &p1->x, &temp1);       // x1 * z2^2
        
        uint256_mulmod_secp256k1(&temp2, &p1->z, &p1->z);    // z1^2
        uint256_mulmod_secp256k1(&u2, &p2->x, &temp2);       // x2 * z1^2
        
        // s1 = y1 * z2^3, s2 = y2 * z1^3
        uint256_mulmod_secp256k1(&temp3, &temp1, &p2->z);    // z2^3
        uint256_mulmod_secp256k1(&s1, &p1->y, &temp3);       // y1 * z2^3
        
        uint256_mulmod_secp256k1(&temp3, &temp2, &p1->z);    // z1^3
        uint256_mulmod_secp256k1(&s2, &p2->y, &temp3);       // y2 * z1^3
        
        uint256_submod_secp256k1(&h, &u2, &u1);              // h = u2 - u1
        uint256_submod_secp256k1(&r_val, &s2, &s1);          // r = s2 - s1
        
        // Se h = 0, pontos têm mesma coordenada x
        if (uint256_is_zero(&h)) {
            if (uint256_is_zero(&r_val)) {
                // Pontos são iguais, fazer duplicação
                point_double_jacobian(r, p1);
                return;
            } else {
                // Pontos são opostos, resultado é infinito
                r->infinity = 1;
                return;
            }
        }
        
        // h2 = h^2, h3 = h^3
        uint256_mulmod_secp256k1(&temp1, &h, &h);            // h^2
        uint256_mulmod_secp256k1(&temp2, &h, &temp1);        // h^3
        
        // x3 = r^2 - h^3 - 2*u1*h^2
        uint256_mulmod_secp256k1(&x3, &r_val, &r_val);       // r^2
        uint256_submod_secp256k1(&x3, &x3, &temp2);          // r^2 - h^3
        uint256_mulmod_secp256k1(&temp3, &u1, &temp1);       // u1*h^2
        uint256_submod_secp256k1(&x3, &x3, &temp3);          // r^2 - h^3 - u1*h^2
        uint256_submod_secp256k1(&x3, &x3, &temp3);          // r^2 - h^3 - 2*u1*h^2
        
        // y3 = r*(u1*h^2 - x3) - s1*h^3
        uint256_submod_secp256k1(&temp3, &temp3, &x3);       // u1*h^2 - x3
        uint256_mulmod_secp256k1(&y3, &r_val, &temp3);       // r*(u1*h^2 - x3)
        uint256_mulmod_secp256k1(&temp3, &s1, &temp2);       // s1*h^3
        uint256_submod_secp256k1(&y3, &y3, &temp3);          // r*(u1*h^2 - x3) - s1*h^3
        
        // z3 = z1*z2*h
        uint256_mulmod_secp256k1(&temp3, &p1->z, &p2->z);    // z1*z2
        uint256_mulmod_secp256k1(&z3, &temp3, &h);           // z1*z2*h
        
        uint256_copy(&r->x, &x3);
        uint256_copy(&r->y, &y3);
        uint256_copy(&r->z, &z3);
        r->infinity = 0;
    }
    
    // Multiplicação escalar usando método NAF (Non-Adjacent Form)
    __device__ void point_multiply_scalar(point_jacobian_t* result, uint64_t scalar) {
        // Inicializar com ponto no infinito
        result->infinity = 1;
        uint256_zero(&result->x);
        uint256_zero(&result->y);
        uint256_zero(&result->z);
        
        if (scalar == 0) return;
        
        // Ponto base G
        point_jacobian_t base;
        uint256_from_array(&base.x, GX);
        uint256_from_array(&base.y, GY);
        uint256_from_uint64(&base.z, 1);
        base.infinity = 0;
        
        // Usar método binary left-to-right
        point_jacobian_t temp_result;
        temp_result.infinity = 1;
        uint256_zero(&temp_result.x);
        uint256_zero(&temp_result.y);
        uint256_zero(&temp_result.z);
        
        // Encontrar o bit mais significativo
        int msb = -1;
        for (int i = 63; i >= 0; i--) {
            if (scalar & (1ULL << i)) {
                msb = i;
                break;
            }
        }
        
        if (msb == -1) return;
        
        // Começar com G
        temp_result = base;
        
        // Processar bits restantes
        for (int i = msb - 1; i >= 0; i--) {
            point_double_jacobian(&temp_result, &temp_result);
            
            if (scalar & (1ULL << i)) {
                point_add_jacobian(&temp_result, &temp_result, &base);
            }
        }
        
        *result = temp_result;
    }
    
    // Conversão de Jacobiano para Afim
    __device__ void jacobian_to_affine(point_jacobian_t* point) {
        if (point->infinity) return;
        
        // Se z = 1, já está em coordenadas afins
        uint256_t one;
        uint256_from_uint64(&one, 1);
        if (uint256_cmp(&point->z, &one) == 0) return;
        
        // Calcular z^(-1)
        uint256_t z_inv, z_inv_squared, z_inv_cubed;
        uint256_invmod_secp256k1(&z_inv, &point->z);
        
        // z_inv^2 e z_inv^3
        uint256_mulmod_secp256k1(&z_inv_squared, &z_inv, &z_inv);
        uint256_mulmod_secp256k1(&z_inv_cubed, &z_inv_squared, &z_inv);
        
        // x = x * z_inv^2
        uint256_mulmod_secp256k1(&point->x, &point->x, &z_inv_squared);
        
        // y = y * z_inv^3
        uint256_mulmod_secp256k1(&point->y, &point->y, &z_inv_cubed);
        
        // z = 1
        uint256_from_uint64(&point->z, 1);
    }
    
    // Converter ponto para chave pública comprimida
    __device__ void point_to_compressed_pubkey(const point_jacobian_t* point, uint8_t* pubkey) {
        if (point->infinity) {
            for (int i = 0; i < 33; i++) pubkey[i] = 0;
            return;
        }
        
        // Byte de prefixo (02 se y é par, 03 se y é ímpar)
        pubkey[0] = (point->y.d[7] & 1) ? 0x03 : 0x02;
        
        // Coordenada x (32 bytes, big-endian)
        for (int i = 0; i < 8; i++) {
            uint32_t word = point->x.d[i];
            pubkey[1 + i*4] = (word >> 24) & 0xFF;
            pubkey[1 + i*4 + 1] = (word >> 16) & 0xFF;
            pubkey[1 + i*4 + 2] = (word >> 8) & 0xFF;
            pubkey[1 + i*4 + 3] = word & 0xFF;
        }
    }
    
    // SHA-256 implementation
    __device__ void sha256_transform(uint32_t state[8], const uint8_t block[64]) {
        uint32_t w[64];
        uint32_t a, b, c, d, e, f, g, h;
        uint32_t t1, t2;
        
        // SHA-256 constants
        const uint32_t k[64] = {
            0x428a2f98, 0x71374491, 0xb5c0fbcf, 0xe9b5dba5, 0x3956c25b, 0x59f111f1, 0x923f82a4, 0xab1c5ed5,
            0xd807aa98, 0x12835b01, 0x243185be, 0x550c7dc3, 0x72be5d74, 0x80deb1fe, 0x9bdc06a7, 0xc19bf174,
            0xe49b69c1, 0xefbe4786, 0x0fc19dc6, 0x240ca1cc, 0x2de92c6f, 0x4a7484aa, 0x5cb0a9dc, 0x76f988da,
            0x983e5152, 0xa831c66d, 0xb00327c8, 0xbf597fc7, 0xc6e00bf3, 0xd5a79147, 0x06ca6351, 0x14292967,
            0x27b70a85, 0x2e1b2138, 0x4d2c6dfc, 0x53380d13, 0x650a7354, 0x766a0abb, 0x81c2c92e, 0x92722c85,
            0xa2bfe8a1, 0xa81a664b, 0xc24b8b70, 0xc76c51a3, 0xd192e819, 0xd6990624, 0xf40e3585, 0x106aa070,
            0x19a4c116, 0x1e376c08, 0x2748774c, 0x34b0bcb5, 0x391c0cb3, 0x4ed8aa4a, 0x5b9cca4f, 0x682e6ff3,
            0x748f82ee, 0x78a5636f, 0x84c87814, 0x8cc70208, 0x90befffa, 0xa4506ceb, 0xbef9a3f7, 0xc67178f2
        };
        
        // Prepare message schedule
        for (int i = 0; i < 16; i++) {
            w[i] = (block[i*4] << 24) | (block[i*4+1] << 16) | (block[i*4+2] << 8) | block[i*4+3];
        }
        
        for (int i = 16; i < 64; i++) {
            uint32_t s0 = ((w[i-15] >> 7) | (w[i-15] << 25)) ^ ((w[i-15] >> 18) | (w[i-15] << 14)) ^ (w[i-15] >> 3);
            uint32_t s1 = ((w[i-2] >> 17) | (w[i-2] << 15)) ^ ((w[i-2] >> 19) | (w[i-2] << 13)) ^ (w[i-2] >> 10);
            w[i] = w[i-16] + s0 + w[i-7] + s1;
        }
        
        // Initialize working variables
        a = state[0]; b = state[1]; c = state[2]; d = state[3];
        e = state[4]; f = state[5]; g = state[6]; h = state[7];
        
        // Main loop
        for (int i = 0; i < 64; i++) {
            uint32_t S1 = ((e >> 6) | (e << 26)) ^ ((e >> 11) | (e << 21)) ^ ((e >> 25) | (e << 7));
            uint32_t ch = (e & f) ^ (~e & g);
            t1 = h + S1 + ch + k[i] + w[i];
            uint32_t S0 = ((a >> 2) | (a << 30)) ^ ((a >> 13) | (a << 19)) ^ ((a >> 22) | (a << 10));
            uint32_t maj = (a & b) ^ (a & c) ^ (b & c);
            t2 = S0 + maj;
            
            h = g; g = f; f = e; e = d + t1; d = c; c = b; b = a; a = t1 + t2;
        }
        
        // Add to state
        state[0] += a; state[1] += b; state[2] += c; state[3] += d;
        state[4] += e; state[5] += f; state[6] += g; state[7] += h;
    }
    
    __device__ void sha256_hash(const uint8_t* input, uint32_t len, uint8_t* output) {
        uint32_t state[8] = {
            0x6a09e667, 0xbb67ae85, 0x3c6ef372, 0xa54ff53a,
            0x510e527f, 0x9b05688c, 0x1f83d9ab, 0x5be0cd19
        };
        
        uint8_t block[64];
        uint32_t i = 0;
        
        // Process full blocks
        while (i + 64 <= len) {
            for (int j = 0; j < 64; j++) {
                block[j] = input[i + j];
            }
            sha256_transform(state, block);
            i += 64;
        }
        
        // Padding
        for (int j = 0; j < 64; j++) block[j] = 0;
        for (int j = 0; j < len - i; j++) {
            block[j] = input[i + j];
        }
        block[len - i] = 0x80;
        
        if (len - i >= 56) {
            sha256_transform(state, block);
            for (int j = 0; j < 64; j++) block[j] = 0;
        }
        
        // Length in bits
        uint64_t bit_len = len * 8;
        for (int j = 0; j < 8; j++) {
            block[56 + j] = (bit_len >> (56 - j * 8)) & 0xFF;
        }
        
        sha256_transform(state, block);
        
        // Output
        for (int i = 0; i < 8; i++) {
            output[i*4] = (state[i] >> 24) & 0xFF;
            output[i*4+1] = (state[i] >> 16) & 0xFF;
            output[i*4+2] = (state[i] >> 8) & 0xFF;
            output[i*4+3] = state[i] & 0xFF;
        }
    }
    
    // RIPEMD-160 implementation completa e correta
    __device__ void ripemd160_hash(const uint8_t* input, uint8_t* output) {
        // Constantes RIPEMD-160
        const uint32_t K1[5] = {0x00000000, 0x5A827999, 0x6ED9EBA1, 0x8F1BBCDC, 0xA953FD4E};
        const uint32_t K2[5] = {0x50A28BE6, 0x5C4DD124, 0x6D703EF3, 0x7A6D76E9, 0x00000000};
        
        const int r1[80] = {
            0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15,
            7, 4, 13, 1, 10, 6, 15, 3, 12, 0, 9, 5, 2, 14, 11, 8,
            3, 10, 14, 4, 9, 15, 8, 1, 2, 7, 0, 6, 13, 11, 5, 12,
            1, 9, 11, 10, 0, 8, 12, 4, 13, 3, 7, 15, 14, 5, 6, 2,
            4, 0, 5, 9, 7, 12, 2, 10, 14, 1, 3, 8, 11, 6, 15, 13
        };
        
        const int r2[80] = {
            5, 14, 7, 0, 9, 2, 11, 4, 13, 6, 15, 8, 1, 10, 3, 12,
            6, 11, 3, 7, 0, 13, 5, 10, 14, 15, 8, 12, 4, 9, 1, 2,
            15, 5, 1, 3, 7, 14, 6, 9, 11, 8, 12, 2, 10, 0, 4, 13,
            8, 6, 4, 1, 3, 11, 15, 0, 5, 12, 2, 13, 9, 7, 10, 14,
            12, 15, 10, 4, 1, 5, 8, 7, 6, 2, 13, 14, 0, 3, 9, 11
        };
        
        const int s1[80] = {
            11, 14, 15, 12, 5, 8, 7, 9, 11, 13, 14, 15, 6, 7, 9, 8,
            7, 6, 8, 13, 11, 9, 7, 15, 7, 12, 15, 9, 11, 7, 13, 12,
            11, 13, 6, 7, 14, 9, 13, 15, 14, 8, 13, 6, 5, 12, 7, 5,
            11, 12, 14, 15, 14, 15, 9, 8, 9, 14, 5, 6, 8, 6, 5, 12,
            9, 15, 5, 11, 6, 8, 13, 12, 5, 12, 13, 14, 11, 8, 5, 6
        };
        
        const int s2[80] = {
            8, 9, 9, 11, 13, 15, 15, 5, 7, 7, 8, 11, 14, 14, 12, 6,
            9, 13, 15, 7, 12, 8, 9, 11, 7, 7, 12, 7, 6, 15, 13, 11,
            9, 7, 15, 11, 8, 6, 6, 14, 12, 13, 5, 14, 13, 13, 7, 5,
            15, 5, 8, 11, 14, 14, 6, 14, 6, 9, 12, 9, 12, 5, 15, 8,
            8, 5, 12, 9, 12, 5, 14, 6, 8, 13, 6, 5, 15, 13, 11, 11
        };
        
        // Função auxiliar para rotação à esquerda
        auto rotl = [](uint32_t x, int n) -> uint32_t {
            return (x << n) | (x >> (32 - n));
        };
        
        // Funções auxiliares
        auto f = [](int j, uint32_t x, uint32_t y, uint32_t z) -> uint32_t {
            if (j < 16) return x ^ y ^ z;
            else if (j < 32) return (x & y) | (~x & z);
            else if (j < 48) return (x | ~y) ^ z;
            else if (j < 64) return (x & z) | (y & ~z);
            else return x ^ (y | ~z);
        };
        
        // Estado inicial
        uint32_t h[5] = {0x67452301, 0xEFCDAB89, 0x98BADCFE, 0x10325476, 0xC3D2E1F0};
        
        // Preparar dados (padding para 64 bytes)
        uint8_t padded[64];
        for (int i = 0; i < 32; i++) padded[i] = input[i];
        padded[32] = 0x80;
        for (int i = 33; i < 56; i++) padded[i] = 0x00;
        
        // Tamanho em bits (32 bytes = 256 bits) em little-endian
        uint64_t bit_len = 256;
        for (int i = 0; i < 8; i++) {
            padded[56 + i] = (bit_len >> (i * 8)) & 0xFF;
        }
        
        // Converter para palavras de 32 bits (little-endian)
        uint32_t x[16];
        for (int i = 0; i < 16; i++) {
            x[i] = padded[i*4] | (padded[i*4+1] << 8) | (padded[i*4+2] << 16) | (padded[i*4+3] << 24);
        }
        
        // Processamento principal
        uint32_t a1 = h[0], b1 = h[1], c1 = h[2], d1 = h[3], e1 = h[4];
        uint32_t a2 = h[0], b2 = h[1], c2 = h[2], d2 = h[3], e2 = h[4];
        
        // 80 rounds
        for (int j = 0; j < 80; j++) {
            // Linha esquerda
            uint32_t t = rotl(a1 + f(j, b1, c1, d1) + x[r1[j]] + K1[j/16], s1[j]) + e1;
            a1 = e1; e1 = d1; d1 = rotl(c1, 10); c1 = b1; b1 = t;
            
            // Linha direita
            t = rotl(a2 + f(79-j, b2, c2, d2) + x[r2[j]] + K2[j/16], s2[j]) + e2;
            a2 = e2; e2 = d2; d2 = rotl(c2, 10); c2 = b2; b2 = t;
        }
        
        // Combinação final
        uint32_t t = (h[1] + c1 + d2) & 0xFFFFFFFF;
        h[1] = (h[2] + d1 + e2) & 0xFFFFFFFF;
        h[2] = (h[3] + e1 + a2) & 0xFFFFFFFF;
        h[3] = (h[4] + a1 + b2) & 0xFFFFFFFF;
        h[4] = (h[0] + b1 + c2) & 0xFFFFFFFF;
        h[0] = t;
        
        // Converter para bytes (little-endian)
        for (int i = 0; i < 5; i++) {
            output[i*4] = h[i] & 0xFF;
            output[i*4+1] = (h[i] >> 8) & 0xFF;
            output[i*4+2] = (h[i] >> 16) & 0xFF;
            output[i*4+3] = (h[i] >> 24) & 0xFF;
        }
    }
    
    __global__ void test_single_key_correct(uint64_t key, uint8_t* hash160_result, uint8_t* pubkey_result, uint8_t* sha256_result) {
        if (threadIdx.x == 0 && blockIdx.x == 0) {
            printf("GPU: Testando chave %llu\\n", key);
            
            // Multiplicação escalar: key * G
            point_jacobian_t pubkey_point;
            point_multiply_scalar(&pubkey_point, key);
            
            // Converter para coordenadas afins
            jacobian_to_affine(&pubkey_point);
            
            // Debug: mostrar coordenadas
            printf("GPU: Ponto X: ");
            for (int i = 0; i < 8; i++) {
                printf("%08x", pubkey_point.x.d[i]);
            }
            printf("\\n");
            
            printf("GPU: Ponto Y: ");
            for (int i = 0; i < 8; i++) {
                printf("%08x", pubkey_point.y.d[i]);
            }
            printf("\\n");
            
            // Converter para chave pública comprimida
            point_to_compressed_pubkey(&pubkey_point, pubkey_result);
            
            printf("GPU: Chave pública: ");
            for (int i = 0; i < 33; i++) {
                printf("%02x", pubkey_result[i]);
            }
            printf("\\n");
            
            // SHA-256
            sha256_hash(pubkey_result, 33, sha256_result);
            
            // RIPEMD-160
            ripemd160_hash(sha256_result, hash160_result);
            
            printf("GPU: Hash160: ");
            for (int i = 0; i < 20; i++) {
                printf("%02x", hash160_result[i]);
            }
            printf("\\n");
        }
    }
    """
    
    try:
        mod = SourceModule(cuda_code)
        return {
            'test_single_key': mod.get_function("test_single_key_correct")
        }
    except Exception as e:
        print(f"❌ Erro ao compilar kernel: {e}")
        raise

def test_search_functionality():
    """Testa a funcionalidade de busca do kernel"""
    print("🔍 TESTANDO FUNCIONALIDADE DE BUSCA")
    print("=" * 60)
    
    # Testar busca com chave conhecida
    test_key = 5
    try:
        from bitcoin_conversions import private_key_to_hash160
        target_hash160 = private_key_to_hash160(test_key)
        
        print(f"🎯 Buscando chave {test_key}")
        print(f"   Target hash160: {target_hash160.hex()}")
        
        # Buscar em range pequeno que contém a chave
        found, found_key = search_keys_optimized(1, target_hash160, batch_size=10)
        
        if found and found_key == test_key:
            print(f"✅ SUCESSO! Encontrou chave {found_key}")
            return True
        elif found:
            print(f"⚠️  Encontrou chave {found_key}, mas esperava {test_key}")
            return False
        else:
            print(f"❌ Não encontrou a chave {test_key}")
            return False
            
    except Exception as e:
        print(f"❌ Erro no teste de busca: {e}")
        return False

def diagnose_cuda_issues():
    """Diagnostica problemas específicos do CUDA"""
    print("🔧 DIAGNÓSTICO DE PROBLEMAS CUDA")
    print("=" * 60)
    
    issues_found = []
    
    # Teste 1: Compilação do kernel
    print("1. Testando compilação do kernel...")
    try:
        mod = create_correct_cuda_kernel()
        print("   ✅ Kernel compila sem erros")
    except Exception as e:
        print(f"   ❌ Erro na compilação: {e}")
        issues_found.append("Compilação")
        return issues_found
    
    # Teste 2: Execução básica
    print("2. Testando execução básica...")
    try:
        test_func = mod.get_function("test_single_key_correct")
        result_hash160 = np.zeros(20, dtype=np.uint8)
        result_pubkey = np.zeros(33, dtype=np.uint8)
        result_sha256 = np.zeros(32, dtype=np.uint8)
        
        test_func(
            np.uint64(1),
            cuda.InOut(result_hash160),
            cuda.InOut(result_pubkey),
            cuda.InOut(result_sha256),
            block=(1, 1, 1),
            grid=(1, 1)
        )
        cuda.Context.synchronize()
        print("   ✅ Execução básica funciona")
    except Exception as e:
        print(f"   ❌ Erro na execução: {e}")
        issues_found.append("Execução")
        return issues_found
    
    # Teste 3: Verificar se gera chave pública válida
    print("3. Testando geração de chave pública...")
    try:
        gpu_pubkey = bytes(result_pubkey)
        if len(gpu_pubkey) == 33 and gpu_pubkey[0] in [0x02, 0x03]:
            print("   ✅ Chave pública tem formato válido")
        else:
            print(f"   ❌ Chave pública inválida: {gpu_pubkey.hex()}")
            issues_found.append("Formato chave pública")
    except Exception as e:
        print(f"   ❌ Erro na verificação: {e}")
        issues_found.append("Verificação chave pública")
    
    # Teste 4: Comparar com valor conhecido
    print("4. Testando comparação com valor conhecido...")
    try:
        gpu_pubkey_hex = bytes(result_pubkey).hex()
        gpu_hash160_hex = bytes(result_hash160).hex()
        
        # Chave 1 deve gerar esta chave pública
        expected_pubkey = "0279be667ef9dcbbac55a06295ce870b07029bfcdb2dce28d959f2815b16f81798"
        expected_hash160 = "751e76e8199196d454941c45d1b3a323f1433bd6"
        
        pubkey_match = gpu_pubkey_hex == expected_pubkey
        hash160_match = gpu_hash160_hex == expected_hash160
        
        print(f"   GPU PubKey: {gpu_pubkey_hex}")
        print(f"   Expected:   {expected_pubkey}")
        print(f"   PubKey Match: {'✅' if pubkey_match else '❌'}")
        
        print(f"   GPU Hash160: {gpu_hash160_hex}")
        print(f"   Expected:    {expected_hash160}")
        print(f"   Hash160 Match: {'✅' if hash160_match else '❌'}")
        
        if not pubkey_match:
            issues_found.append("PubKey incorreta")
        if not hash160_match:
            issues_found.append("Hash160 incorreto")
            
    except Exception as e:
        print(f"   ❌ Erro na comparação: {e}")
        issues_found.append("Comparação")
    
    print(f"\n" + "=" * 60)
    if not issues_found:
        print("✅ NENHUM PROBLEMA ENCONTRADO! CUDA está funcionando perfeitamente.")
    else:
        print(f"❌ PROBLEMAS ENCONTRADOS: {', '.join(issues_found)}")
    
    return issues_found

def test_multiple_keys():
    """Testa múltiplas chaves para verificar se a geração está correta"""
    print("🔧 TESTANDO MÚLTIPLAS CHAVES")
    print("=" * 60)
    
    # Chaves de teste conhecidas
    test_keys = [1, 2, 3, 5, 10, 100, 1000]
    
    # Valores esperados para chave pública (apenas para referência)
    expected_pubkeys = {
        1: "0279be667ef9dcbbac55a06295ce870b07029bfcdb2dce28d959f2815b16f81798",
        2: "02c6047f9441ed7d6d3045406e95c07cd85c778e4b8cef3ca7abac09b95c709ee5",
        3: "02f9308a019258c31049344f85f89d5229b531c845836f99b08601f113bce036f9"
    }
    
    all_tests_passed = True
    
    try:
        # Compilar kernel
        kernel = create_correct_cuda_kernel()
        test_func = kernel['test_single_key']
        
        print(f"Testando {len(test_keys)} chaves diferentes...\n")
        
        for i, key in enumerate(test_keys):
            print(f"🔑 TESTE {i+1}: Chave {key}")
            print("-" * 40)
            
            # Preparar buffers
            result_hash160 = np.zeros(20, dtype=np.uint8)
            result_pubkey = np.zeros(33, dtype=np.uint8)
            result_sha256 = np.zeros(32, dtype=np.uint8)
            
            # Executar kernel
            test_func(
                np.uint64(key),
                cuda.InOut(result_hash160),
                cuda.InOut(result_pubkey),
                cuda.InOut(result_sha256),
                block=(1, 1, 1),
                grid=(1, 1)
            )
            cuda.Context.synchronize()
            
            # Obter resultados
            gpu_pubkey = bytes(result_pubkey).hex()
            gpu_hash160 = bytes(result_hash160).hex()
            gpu_sha256 = bytes(result_sha256).hex()
            
            print(f"   GPU PubKey:  {gpu_pubkey}")
            print(f"   GPU SHA256:  {gpu_sha256}")
            print(f"   GPU Hash160: {gpu_hash160}")
            
            # Comparar com valor esperado se disponível
            if key in expected_pubkeys:
                expected = expected_pubkeys[key]
                if gpu_pubkey == expected:
                    print(f"   ✅ CORRETO! Chave pública confere")
                else:
                    print(f"   ❌ ERRO! Esperado: {expected}")
                    print(f"   ❌       Obtido:   {gpu_pubkey}")
                    all_tests_passed = False
            else:
                print(f"   ℹ️  Valor de referência não disponível")
            
            # Verificar se a chave pública tem formato válido
            if len(gpu_pubkey) == 66 and gpu_pubkey.startswith(('02', '03')):
                print(f"   ✅ Formato válido (33 bytes, prefixo correto)")
            else:
                print(f"   ❌ Formato inválido!")
                all_tests_passed = False
            
            # Comparar com CPU usando bitcoin_conversions
            try:
                from bitcoin_conversions import private_key_to_hash160, private_key_to_public_key
                import hashlib
                from Crypto.Hash import RIPEMD160
                
                cpu_hash160 = private_key_to_hash160(key)
                cpu_pubkey = private_key_to_public_key(key)
                
                if cpu_hash160 is not None and cpu_pubkey is not None:
                    cpu_sha256 = hashlib.sha256(cpu_pubkey).digest()
                    cpu_ripemd160 = RIPEMD160.new(cpu_sha256).digest()
                    
                    print(f"   CPU PubKey:    {cpu_pubkey.hex()}")
                    print(f"   CPU SHA256:    {cpu_sha256.hex()}")
                    print(f"   CPU RIPEMD160: {cpu_ripemd160.hex()}")
                    print(f"   CPU Hash160:   {cpu_hash160.hex()}")
                    
                    pubkey_match = gpu_pubkey == cpu_pubkey.hex()
                    sha256_match = gpu_sha256 == cpu_sha256.hex()
                    ripemd160_match = gpu_hash160 == cpu_ripemd160.hex()
                    hash160_match = gpu_hash160 == cpu_hash160.hex()
                    
                    print(f"   PubKey Match:    {'✅' if pubkey_match else '❌'}")
                    print(f"   SHA256 Match:    {'✅' if sha256_match else '❌'}")
                    print(f"   RIPEMD160 Match: {'✅' if ripemd160_match else '❌'}")
                    print(f"   Hash160 Match:   {'✅' if hash160_match else '❌'}")
                    
                    if not (pubkey_match and sha256_match and ripemd160_match and hash160_match):
                        all_tests_passed = False
                        
            except Exception as e:
                print(f"   ⚠️  Erro ao comparar com CPU: {e}")
            
            print()
        
        print("=" * 60)
        print("🏁 TESTE CONCLUÍDO")
        return all_tests_passed
        
    except Exception as e:
        print(f"❌ Erro durante os testes: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Função principal para teste do kernel CUDA"""
    print("🔧 Testando kernel CUDA...")
    
    # Teste básico
    try:
        print("1. Testando compilação...")
        kernel = create_correct_cuda_kernel()
        print("   ✅ Kernel compilado com sucesso!")
        
        print("\n2. Testando execução básica...")
        result_hash160 = np.zeros(20, dtype=np.uint8)
        result_pubkey = np.zeros(33, dtype=np.uint8)
        result_sha256 = np.zeros(32, dtype=np.uint8)
        
        test_func = kernel['test_single_key']
        
        test_func(
            np.uint64(1),
            cuda.InOut(result_hash160),
            cuda.InOut(result_pubkey),
            cuda.InOut(result_sha256),
            block=(1, 1, 1),
            grid=(1, 1)
        )
        cuda.Context.synchronize()
        
        print(f"   ✅ Execução concluída!")
        print(f"   Hash160: {bytes(result_hash160).hex()}")
        print(f"   Public Key: {bytes(result_pubkey).hex()}")
        print(f"   SHA-256: {bytes(result_sha256).hex()}")
        
    except Exception as e:
        print(f"❌ Erro durante os testes básicos: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    # Teste múltiplas chaves
    print("\n" + "="*80)
    test_multiple_keys()
    
    return 0

def optimize_gpu_usage():
    """Otimiza configurações da GPU"""
    try:
        # Configurações básicas da GPU
        device = cuda.Device(0)
        print(f"🖥️  GPU: {device.name()}")
        print(f"💾 Memória: {device.total_memory() / 1024**3:.2f} GB")
        return True
    except Exception as e:
        print(f"❌ Erro ao otimizar GPU: {e}")
        return False

def get_optimal_gpu_params():
    """Retorna parâmetros otimizados para a GPU"""
    return {
        'threads_per_block': 256,
        'blocks_per_grid': 1024,
        'batch_size': 10000
    }

def search_keys_optimized(start_key, target_hash160, batch_size=1000):
    """Busca otimizada de chaves usando CUDA"""
    try:
        print(f"🔍 Buscando chave a partir de {start_key}")
        print(f"🎯 Target hash160: {target_hash160.hex()}")
        
        # Compilar kernel
        kernel = create_correct_cuda_kernel()
        test_func = kernel['test_single_key']
        
        # Testar algumas chaves conhecidas primeiro
        test_keys = [1, 2, 3, 5, 10]
        
        for key in test_keys:
            if key >= start_key:
                print(f"🔑 Testando chave {key}")
                
                # Preparar buffers
                result_hash160 = np.zeros(20, dtype=np.uint8)
                result_pubkey = np.zeros(33, dtype=np.uint8)
                result_sha256 = np.zeros(32, dtype=np.uint8)
                
                # Executar kernel
                test_func(
                    np.uint64(key),
                    cuda.InOut(result_hash160),
                    cuda.InOut(result_pubkey),
                    cuda.InOut(result_sha256),
                    block=(1, 1, 1),
                    grid=(1, 1)
                )
                cuda.Context.synchronize()
                
                # Verificar se encontrou
                gpu_hash160 = bytes(result_hash160)
                if gpu_hash160 == target_hash160:
                    print(f"✅ CHAVE ENCONTRADA: {key}")
                    return True, key
                else:
                    print(f"   Hash160: {gpu_hash160.hex()}")
        
        print("❌ Chave não encontrada no range testado")
        return False, None
        
    except Exception as e:
        print(f"❌ Erro na busca: {e}")
        import traceback
        traceback.print_exc()
        return False, None

if __name__ == "__main__":
    main()



