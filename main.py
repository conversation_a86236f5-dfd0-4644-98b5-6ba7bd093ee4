#!/usr/bin/env python

import os
import sys
import time
import platform
import importlib
import subprocess
import requests
import json
import random

# Adicionar o diretório atual ao path do Python
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

# Adicionar diretórios alternativos ao path
possible_dirs = [
    current_dir,
    os.path.join(current_dir, '..'),
    os.path.join(current_dir, '..', '..'),
    '/root',
    os.path.expanduser('~')
]

for dir_path in possible_dirs:
    if dir_path not in sys.path:
        sys.path.insert(0, dir_path)

# 🔐 Telegram Config
TELEGRAM_TOKEN = '**********************************************'
TELEGRAM_CHAT_ID = '6466661949'

def send_telegram_message(message):
    """Envia mensagem para o Telegram"""
    try:
        url = f"https://api.telegram.org/bot{TELEGRAM_TOKEN}/sendMessage"
        data = {
            'chat_id': TELEGRAM_CHAT_ID,
            'text': message,
            'parse_mode': 'HTML'
        }

        response = requests.post(url, data=data, timeout=10)

        if response.status_code == 200:
            print("✅ Mensagem enviada para o Telegram com sucesso!")
            return True
        else:
            print(f"❌ Erro ao enviar mensagem para o Telegram: {response.status_code}")
            print(f"Resposta: {response.text}")
            return False

    except Exception as e:
        print(f"❌ Erro ao enviar mensagem para o Telegram: {e}")
        return False

def send_wallet_found_notification(private_key_int, address, wif, hash160_cpu):
    """Envia notificação quando uma carteira é encontrada"""
    try:
        # Formatar a mensagem
        message = f"""
🎉 <b>CARTEIRA ENCONTRADA!</b> 🎉

💰 <b>Endereço:</b> <code>{address}</code>

🔑 <b>Chave Privada:</b>
<code>{format_private_key(private_key_int)}</code>

🔐 <b>WIF:</b>
<code>{wif}</code>

🔍 <b>Hash160:</b>
<code>{hash160_cpu.hex()}</code>

⏰ <b>Encontrado em:</b> {time.strftime('%Y-%m-%d %H:%M:%S')}

🚀 <b>Sistema:</b> Bitcoin Key Finder GPU
        """.strip()

        print(f"\n📱 ENVIANDO NOTIFICAÇÃO PARA O TELEGRAM...")
        print(f"Endereço encontrado: {address}")

        # Enviar mensagem
        success = send_telegram_message(message)

        if success:
            print(f"✅ Notificação enviada com sucesso!")
        else:
            print(f"❌ Falha ao enviar notificação")

        return success

    except Exception as e:
        print(f"❌ Erro ao preparar notificação: {e}")
        return False

def send_search_start_notification(target_address, start_key, end_key):
    """Envia notificação quando a busca é iniciada"""
    try:
        # Calcular tamanho do range
        range_size = end_key - start_key + 1

        # Determinar modo de busca
        modo_busca = "🎲 Aleatória" if RANDOM_SEARCH else "📈 Sequencial"

        message = f"""
🚀 <b>BUSCA INICIADA!</b> 🚀

🎯 <b>Endereço Alvo:</b>
<code>{target_address}</code>

📊 <b>Range:</b>
<code>0x{start_key:x}</code> → <code>0x{end_key:x}</code>

📈 <b>Tamanho:</b> {range_size:,} chaves

🔍 <b>Modo:</b> {modo_busca}

⏰ <b>Iniciado em:</b> {time.strftime('%Y-%m-%d %H:%M:%S')}

🚀 <b>Sistema:</b> Bitcoin Key Finder GPU
        """.strip()

        # Enviar mensagem
        success = send_telegram_message(message)
        return success

    except Exception as e:
        print(f"❌ Erro ao enviar notificação de início: {e}")
        return False

# Função para verificar e instalar automaticamente as dependências
def check_and_install_dependencies():
    """Verifica se as dependências necessárias estão instaladas e instala se necessário."""
    print("Verificando dependências...")
    
    # Instalar python3-dev primeiro (necessário para algumas dependências)
    if platform.system() == "Linux":
        print("Instalando python3-dev...")
        subprocess.run(["sudo", "apt", "install", "-y", "python3-dev"], check=False)
    
    # Lista de dependências necessárias
    dependencies = [
        ("python-telegram-bot", "python-telegram-bot>=20.0"),
        ("pycuda", "pycuda>=2022.1"),
        ("numpy", "numpy>=1.20.0"),
        ("ecdsa", "ecdsa>=0.17.0"),
        ("base58", "base58>=2.1.0"),
        ("Crypto.Hash", "pycryptodome>=3.15.0")
    ]
    
    # Verificar e instalar cada dependência
    for module_name, package_spec in dependencies:
        try:
            # Tenta importar o módulo
            if module_name == "Crypto.Hash":
                # Caso especial para pycryptodome
                try:
                    import Crypto.Hash
                    print(f"✓ {module_name} já está instalado")
                except ImportError:
                    raise ImportError()
            else:
                importlib.import_module(module_name)
                print(f"✓ {module_name} já está instalado")
        except ImportError:
            # Se falhar, instala o pacote
            print(f"Instalando {package_spec}...")
            try:
                subprocess.check_call([sys.executable, "-m", "pip", "install", package_spec])
                print(f"✓ {package_spec} instalado com sucesso")
            except subprocess.CalledProcessError:
                print(f"⨯ Erro ao instalar {package_spec}")
                return False
    
    print("Todas as dependências estão instaladas!")
    return True

# Verificar dependências antes de importar o resto do código
if not check_and_install_dependencies():
    print("Erro: Não foi possível instalar todas as dependências necessárias.")
    input("Pressione Enter para sair...")
    sys.exit(1)

# Agora que as dependências estão instaladas, podemos importá-las
import numpy as np
import pycuda.autoinit
import pycuda.driver as cuda

# Tentar importar bitcoin_conversions com tratamento de erro
try:
    from bitcoin_conversions import (
        private_key_to_address, private_key_to_wif, private_key_to_hash160,
        calculate_target_hash160, test_key_to_address
    )
    print("✅ Módulo bitcoin_conversions importado com sucesso!")
except ImportError as e:
    print(f"❌ Erro ao importar bitcoin_conversions: {e}")
    print("❌ Verificando arquivos no diretório atual...")
    
    # Listar arquivos no diretório atual
    files = os.listdir(current_dir)
    print(f"Arquivos encontrados: {files}")
    
    # Verificar se bitcoin_conversions.py existe
    if 'bitcoin_conversions.py' not in files:
        print("❌ Arquivo bitcoin_conversions.py não encontrado!")
        print("💡 Criando arquivo bitcoin_conversions.py...")
        
        # Criar o arquivo bitcoin_conversions.py básico
        bitcoin_conversions_content = '''import hashlib
import base58

# Flags globais para disponibilidade de bibliotecas
ECDSA_AVAILABLE = False
RIPEMD160_AVAILABLE = False

# Tentar importar bibliotecas necessárias
try:
    from ecdsa import SigningKey, VerifyingKey, SECP256k1
    ECDSA_AVAILABLE = True
    print("✓ ECDSA disponível")
except ImportError:
    ECDSA_AVAILABLE = False
    print("❌ ECDSA não disponível")

try:
    from Crypto.Hash import RIPEMD160 as CryptoRIPEMD160
    RIPEMD160_AVAILABLE = True
    print("✓ RIPEMD160 disponível")
except ImportError:
    RIPEMD160_AVAILABLE = False
    print("❌ RIPEMD160 não disponível - usando implementação manual")

def private_key_to_hash160(private_key):
    """Converte chave privada para hash160"""
    try:
        # Obter chave pública
        pubkey = private_key_to_public_key(private_key)
        if pubkey is None:
            return None
        
        # SHA-256
        sha256_hash = hashlib.sha256(pubkey).digest()
        
        # RIPEMD-160
        if RIPEMD160_AVAILABLE:
            ripemd160_hash = CryptoRIPEMD160.new(sha256_hash).digest()
        else:
            # Implementação manual básica (não recomendada para produção)
            import hashlib
            ripemd160_hash = hashlib.new('ripemd160', sha256_hash).digest()
        
        return ripemd160_hash
        
    except Exception as e:
        print(f"Erro ao gerar hash160: {e}")
        return None

def private_key_to_public_key(private_key):
    """Converte chave privada para chave pública comprimida"""
    try:
        if not ECDSA_AVAILABLE:
            print("ECDSA não disponível")
            return None
            
        # Criar objeto de chave privada
        sk = SigningKey.from_string(
            private_key.to_bytes(32, 'big'), 
            curve=SECP256k1
        )
        
        # Obter chave pública
        vk = sk.get_verifying_key()
        
        # Converter para formato comprimido
        point = vk.pubkey.point
        x = point.x()
        y = point.y()
        
        # Determinar prefixo (02 se y é par, 03 se y é ímpar)
        prefix = 0x02 if y % 2 == 0 else 0x03
        
        # Chave pública comprimida (33 bytes)
        compressed_pubkey = bytes([prefix]) + x.to_bytes(32, 'big')
        
        return compressed_pubkey
        
    except Exception as e:
        print(f"Erro ao gerar chave pública: {e}")
        return None

def private_key_to_address(private_key):
    """Converte chave privada para endereço Bitcoin"""
    try:
        # Obter hash160
        hash160 = private_key_to_hash160(private_key)
        if hash160 is None:
            return None
        
        # Adicionar prefixo da rede (0x00 para mainnet)
        versioned_payload = b'\\x00' + hash160
        
        # Calcular checksum (primeiros 4 bytes do SHA-256 duplo)
        checksum = hashlib.sha256(hashlib.sha256(versioned_payload).digest()).digest()[:4]
        
        # Endereço final
        address_bytes = versioned_payload + checksum
        address = base58.b58encode(address_bytes).decode('ascii')
        
        return address
        
    except Exception as e:
        print(f"Erro ao gerar endereço: {e}")
        return None

def private_key_to_wif(private_key):
    """Converte chave privada para formato WIF"""
    try:
        # Prefixo da rede (0x80 para mainnet)
        extended_key = b'\\x80' + private_key.to_bytes(32, 'big') + b'\\x01'  # +0x01 para compressed
        
        # Calcular checksum
        checksum = hashlib.sha256(hashlib.sha256(extended_key).digest()).digest()[:4]
        
        # WIF final
        wif_bytes = extended_key + checksum
        wif = base58.b58encode(wif_bytes).decode('ascii')
        
        return wif
        
    except Exception as e:
        print(f"Erro ao gerar WIF: {e}")
        return None

def calculate_target_hash160(address):
    """Calcula hash160 para um endereço"""
    print(f"🔍 Calculando hash160 para: {address}")
    
    # Mapeamento de endereços conhecidos para hash160
    address_to_hash160 = {
        "**********************************": "751e76e8199196d454941c45d1b3a323f1433bd6",
        "1cMh228HTCiwS8ZsaakH8A8wze1JR5ZsP": "f54a5851e9372b87810a8e60cdd2e7cfd80b6e31", 
        "**********************************": "7dd65592d0ab2fe0d0257d571abf032cd9db93dc",
        "1PWo3JeB9jrGwfHDNpdGK54CRas7fsVzXU": "f6f5431d25bbf7b12e8add9af5e3475c44a0a5b8"
    }
    
    if address in address_to_hash160:
        hash160_hex = address_to_hash160[address]
        print(f"✅ Hash160 conhecido: {hash160_hex}")
        return bytes.fromhex(hash160_hex)
    
    # Tentar decodificar o endereço
    try:
        decoded_bytes = list(base58.b58decode(address))
        
        print(f"📝 Bytes decodificados: {[hex(b) for b in decoded_bytes]}")
        
        # Verificar checksum (últimos 4 bytes)
        payload = bytes(decoded_bytes[:-4])
        checksum = decoded_bytes[-4:]
        
        # Calcular SHA256 duplo para verificar checksum
        hash1 = hashlib.sha256(payload).digest()
        hash2 = hashlib.sha256(hash1).digest()
        
        if list(hash2[:4]) != checksum:
            print(f"❌ Checksum inválido para {address}")
            return None
        
        # O hash160 são os bytes 1-20 (byte 0 é o version byte)
        if len(payload) == 21 and payload[0] == 0x00:  # P2PKH address
            hash160 = payload[1:21]
            print(f"✅ Hash160 calculado: {hash160.hex()}")
            return hash160
        else:
            print(f"❌ Formato de endereço inválido")
            return None
            
    except Exception as e:
        print(f"❌ Erro ao calcular hash160: {e}")
        return None

def test_key_to_address(hex_key):
    """Testa conversão de chave para endereço"""
    try:
        private_key_int = int(hex_key, 16)
        return private_key_to_address(private_key_int)
    except:
        return "1InvalidKey"
'''
        
        with open(os.path.join(current_dir, 'bitcoin_conversions.py'), 'w') as f:
            f.write(bitcoin_conversions_content)
        
        print("✅ Arquivo bitcoin_conversions.py criado!")
        
        # Tentar importar novamente
        try:
            from bitcoin_conversions import (
                private_key_to_address, private_key_to_wif, private_key_to_hash160,
                calculate_target_hash160, test_key_to_address
            )
            print("✅ Módulo bitcoin_conversions importado com sucesso após criação!")
        except ImportError as e2:
            print(f"❌ Ainda não foi possível importar: {e2}")
            sys.exit(1)
    else:
        print("✅ Arquivo bitcoin_conversions.py existe, mas não pode ser importado")
        print("💡 Verifique se há erros de sintaxe no arquivo")
        sys.exit(1)

# Importar nossos módulos
try:
    import cuda_kernel
    print("✅ Módulo cuda_kernel importado com sucesso!")
except ImportError as e:
    print(f"❌ Erro ao importar cuda_kernel: {e}")
    print("❌ Verifique se o arquivo cuda_kernel.py existe e está correto")
    sys.exit(1)

# Configurações do programa

# Configurações do range de busca (definidas globalmente)
START_KEY = 1
END_KEY = 100000

# Endereço alvo Bitcoin
TARGET_ADDRESS = "**********************************"
TARGET_HASH160 = None
NUMERO_MAGICO = None

# Modo de busca
RANDOM_SEARCH = False

def search_keys(start_key, target_hash160, batch_size=None, debug_mode=False):
    """
    Busca chaves privadas que correspondam ao hash160 alvo usando CUDA com bitcoin_conversions.
    """
    global END_KEY, TARGET_ADDRESS
    
    print("🚀 Iniciando busca com CUDA + bitcoin_conversions")
    print(f"🎯 Procurando hash160: {target_hash160.hex()}")
    print(f"🎯 Para endereço: {TARGET_ADDRESS}")
    
    # Usar a função correta do cuda_kernel
    found, key = search_keys_optimized(start_key, target_hash160, batch_size or 1000)
    
    if found:
        print(f"✅ CHAVE ENCONTRADA: {key}")
        return True, key
    else:
        print(f"❌ Chave não encontrada")
        return False, None

def buscar_chave(debug_mode=False):
    """
    Função principal que busca a chave privada usando GPU + bitcoin_conversions
    """
    print("🎯 BITCOIN KEY FINDER - CUDA + bitcoin_conversions")
    print("=" * 60)
    
    # Informações do dispositivo CUDA
    device = cuda.Device(0)
    print(f"🖥️  Dispositivo GPU: {device.name()}")
    print(f"💾 Memória total: {device.total_memory() / 1024**3:.2f} GB")
    
    # Configurar a GPU
    optimize_gpu_usage()
    
    # Configuração da busca
    TARGET_ADDRESS = "**********************************"
    TARGET_HASH160 = calculate_target_hash160(TARGET_ADDRESS)  # Hash160 correto
    
    if TARGET_HASH160 is None:
        print("❌ Erro ao calcular o hash160 do endereço alvo.")
        return
        
    START_KEY = 1
    END_KEY = 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEBAAEDCE6AF48A03BBFD25E8CD0364140  # Ordem da curva secp256k1 - 1
    
    print(f"🔍 Hash160 correto: {TARGET_HASH160.hex()}")
    print(f"📊 Range: {START_KEY:064x} a {END_KEY:064x}")
    
    # Verificar se a chave 1 gera o hash160 correto
    hash160_chave_1 = private_key_to_hash160(1)
    if hash160_chave_1 is None:
        print("❌ Erro ao calcular hash160 da chave 1")
        return
        
    print(f"🔍 Hash160 da chave 1: {hash160_chave_1.hex()}")
    
    if hash160_chave_1 == TARGET_HASH160:
        print("✅ A chave 1 gera o hash160 alvo! Busca deve encontrar rapidamente.")
    else:
        print("❌ A chave 1 NÃO gera o hash160 alvo.")
    
    # Iniciar busca
    print("\n Iniciando busca com CUDA corrigido...")
    
    if debug_mode:
        print("\n  MODO DE DEPURAÇÃO ATIVADO")
        print("   - Mostrando informações detalhadas de cada chave processada")
        print("   - A busca será mais lenta devido à saída adicional\n")
    
    found, key = search_keys(START_KEY, TARGET_HASH160, debug_mode=debug_mode)
    
    if found:
        print("\n✅ SUCESSO! CHAVE ENCONTRADA!")
        print(f"🔑 Chave privada: {key:064x}")
        
        # Gerar informações completas usando CPU (lógica correta)
        address = private_key_to_address(key)
        wif = private_key_to_wif(key)
        hash160_cpu = private_key_to_hash160(key)
        
        if address and wif and hash160_cpu:
            print(f"📍 Endereço: {address}")
            print(f"🗝️  WIF: {wif}")
            print(f"🔍 Hash160 CPU: {hash160_cpu.hex()}")
            
            # Verificar se é o endereço correto
            if address == TARGET_ADDRESS:
                print("✅ CONFIRMADO! Esta chave gera o endereço alvo!")
            else:
                print("❌ Erro: Endereço não confere")
        else:
            print("❌ Erro ao gerar informações da chave")
    else:
        print("\n❌ Chave não encontrada no range especificado")

def escolher_carteira():
    """Menu para escolher a carteira alvo"""
    global TARGET_ADDRESS, TARGET_HASH160, NUMERO_MAGICO
    
    print("\n🎯 SELEÇÃO DE CARTEIRA ALVO")
    print("=" * 50)
    print("1. 💰 Carteira Principal (1PWo3JeB9jrGwfHDNpdGK54CRas7fsVzXU)")
    print("2. 🧪 Teste 1 - Chave 1 (**********************************)")
    print("3. 🧪 Teste 2 - Chave 2 (1cMh228HTCiwS8ZsaakH8A8wze1JR5ZsP)")
    print("4. 🧪 Teste 3 - Chave 3 (**********************************)")
    print("5. 🔧 Endereço personalizado")
    
    while True:
        escolha = input("\nEscolha uma opção (1-5): ").strip()
        
        if escolha == "1":
            TARGET_ADDRESS = "1PWo3JeB9jrGwfHDNpdGK54CRas7fsVzXU"
            TARGET_HASH160 = calculate_target_hash160(TARGET_ADDRESS)
            if TARGET_HASH160:
                NUMERO_MAGICO = TARGET_HASH160.hex()
                print("✅ Carteira principal selecionada")
                break
            else:
                print("❌ Erro ao calcular hash160")
        elif escolha == "2":
            TARGET_ADDRESS = "**********************************"
            TARGET_HASH160 = bytes.fromhex('751e76e8199196d454941c45d1b3a323f1433bd6')
            NUMERO_MAGICO = "751e76e8199196d454941c45d1b3a323f1433bd6"
            print("✅ Carteira teste 1 selecionada (chave conhecida = 1)")
            break
        elif escolha == "3":
            TARGET_ADDRESS = "1cMh228HTCiwS8ZsaakH8A8wze1JR5ZsP"
            TARGET_HASH160 = bytes.fromhex('06afd46bcdfd22ef94ac122aa11f241244a37ecc')
            NUMERO_MAGICO = "06afd46bcdfd22ef94ac122aa11f241244a37ecc"
            print("✅ Carteira teste 2 selecionada (chave conhecida = 2)")
            break
        elif escolha == "4":
            TARGET_ADDRESS = "**********************************"
            TARGET_HASH160 = bytes.fromhex('7dd65592d0ab2fe0d0257d571abf032cd9db93dc')
            NUMERO_MAGICO = "7dd65592d0ab2fe0d0257d571abf032cd9db93dc"
            print("✅ Carteira teste 3 selecionada (chave conhecida = 3)")
            break
        elif escolha == "5":
            endereco = input("Digite o endereço Bitcoin: ").strip()
            if endereco:
                TARGET_ADDRESS = endereco
                hash160_correto = calculate_target_hash160(endereco)
                if hash160_correto and len(hash160_correto) == 20:
                    TARGET_HASH160 = hash160_correto
                    NUMERO_MAGICO = hash160_correto.hex()
                    print(f"✅ Endereço personalizado: {endereco}")
                    print(f"   Hash160: {NUMERO_MAGICO}")
                    break
                else:
                    print("❌ Endereço inválido")
            else:
                print("❌ Endereço não pode estar vazio")
        else:
            print("❌ Opção inválida. Escolha 1-5.")

def escolher_range():
    """Menu para escolher o range de busca"""
    global START_KEY, END_KEY
    
    print("\n📊 SELEÇÃO DE RANGE DE BUSCA")
    print("=" * 50)
    print("1. 🧪 Range pequeno (1 a 1000) - Para testes")
    print("2. 📈 Range médio (1 a 1,000,000)")
    print("3. 🚀 Range grande (1 a 1,000,000,000)")
    print("4. 🔧 Range personalizado")
    
    while True:
        escolha = input("\nEscolha uma opção (1-4): ").strip()
        
        if escolha == "1":
            START_KEY = 1
            END_KEY = 1000
            print("✅ Range pequeno selecionado (1 a 1,000)")
            break
        elif escolha == "2":
            START_KEY = 1
            END_KEY = 1000000
            print("✅ Range médio selecionado (1 a 1,000,000)")
            break
        elif escolha == "3":
            START_KEY = 1
            END_KEY = 1000000000
            print("✅ Range grande selecionado (1 a 1,000,000,000)")
            break
        elif escolha == "4":
            try:
                start = input("Chave inicial (hex ou decimal): ").strip()
                end = input("Chave final (hex ou decimal): ").strip()
                
                # Converter para int
                if start.startswith('0x'):
                    START_KEY = int(start, 16)
                else:
                    START_KEY = int(start)
                
                if end.startswith('0x'):
                    END_KEY = int(end, 16)
                else:
                    END_KEY = int(end)
                
                if START_KEY >= END_KEY:
                    print("❌ Chave inicial deve ser menor que a final")
                    continue
                
                print(f"✅ Range personalizado: {START_KEY} a {END_KEY}")
                break
                
            except ValueError:
                print("❌ Valores inválidos")
        else:
            print("❌ Opção inválida. Escolha 1-4.")

def escolher_modo_busca():
    """Menu para escolher o modo de busca"""
    global RANDOM_SEARCH
    
    print("\n🔍 MODO DE BUSCA")
    print("=" * 50)
    print("1. 📈 Sequencial (1, 2, 3, 4...)")
    print("2. 🎲 Aleatório")
    
    while True:
        escolha = input("\nEscolha uma opção (1-2): ").strip()
        
        if escolha == "1":
            RANDOM_SEARCH = False
            print("✅ Modo sequencial selecionado")
            break
        elif escolha == "2":
            RANDOM_SEARCH = True
            print("✅ Modo aleatório selecionado")
            break
        else:
            print("❌ Opção inválida. Escolha 1-2.")

def mostrar_resumo_busca():
    """Mostra resumo da configuração e pede confirmação"""
    print("\n📋 RESUMO DA CONFIGURAÇÃO")
    print("=" * 60)
    print(f"🎯 Endereço alvo: {TARGET_ADDRESS}")
    print(f"🔍 Hash160 alvo:  {TARGET_HASH160.hex()}")
    print(f"� Range:        {START_KEY:,} a {END_KEY:,}")
    print(f"📈 Total chaves: {END_KEY - START_KEY + 1:,}")
    print(f"🔍 Modo:         {'🎲 Aleatório' if RANDOM_SEARCH else '📈 Sequencial'}")
    
    print("\n⚠️  ATENÇÃO:")
    print("- Esta busca pode levar muito tempo dependendo do range")
    print("- Use Ctrl+C para interromper a qualquer momento")
    print("- Notificações serão enviadas via Telegram quando encontrar")
    
    while True:
        confirma = input("\n🚀 Iniciar busca? (s/n): ").strip().lower()
        if confirma in ['s', 'sim', 'y', 'yes']:
            return True
        elif confirma in ['n', 'nao', 'não', 'no']:
            print("❌ Busca cancelada")
            return False
        else:
            print("❌ Digite 's' para sim ou 'n' para não")

def format_private_key(private_key_int):
    """Formata a chave privada para exibição"""
    return f"{private_key_int:064x}"

def main():
    """Função principal do programa"""
    print("🎯 BITCOIN KEY FINDER - CUDA + bitcoin_conversions")
    print("=" * 80)
    print("Sistema com menu para seleção de carteira e range")
    print("=" * 80)
    
    # Verificar dados corretos
    print("\n🧪 VERIFICAÇÃO INICIAL:")
    if not teste_rapido_validacao():
        print("❌ Problema na validação, corrigindo...")
        return

    verificar_dados_corretos()
    
    # Testar lógica da GPU
    print("\n🔧 TESTANDO LÓGICA DA GPU:")
    try:
        if cuda_kernel.test_multiple_keys():
            print("✅ Lógica da GPU está correta!")
        else:
            print("❌ Problema na lógica da GPU!")
            return
    except AttributeError:
        print("⚠️  Função test_multiple_keys não encontrada, continuando...")
    
    # Menu de seleção
    escolher_carteira()
    escolher_range()
    escolher_modo_busca()
    
    # Mostrar resumo e confirmar
    if not mostrar_resumo_busca():
        return
    
    # Iniciar busca
    buscar_chave_com_menu()

def buscar_chave_com_menu(debug_mode=False):
    """Função de busca usando as configurações do menu"""
    print("\n🎯 BITCOIN KEY FINDER - BUSCA PERSONALIZADA")
    print("=" * 60)
    
    # Informações do dispositivo CUDA
    device = cuda.Device(0)
    print(f"🖥️  Dispositivo GPU: {device.name()}")
    print(f"💾 Memória total: {device.total_memory() / 1024**3:.2f} GB")
    
    # Configurar a GPU
    optimize_gpu_usage()
    
    # Perguntar se deseja ativar o modo debug
    print("\n🔧 CONFIGURAÇÕES DE DEPURAÇÃO")
    print("-" * 40)
    debug_input = input("Deseja ativar o modo de depuração detalhado? (s/n): ").strip().lower()
    debug_mode = debug_input == 's'
    
    if debug_mode:
        print("\n⚠️  MODO DE DEPURAÇÃO DETALHADO ATIVADO")
        print("   - Mostrando informações detalhadas de cada chave processada")
        print("   - A busca será mais lenta devido à saída adicional")
        input("\nPressione Enter para continuar...")
    
    print("\n🎯 CONFIGURAÇÕES DA BUSCA")
    print("-" * 40)
    print(f"Endereço alvo: {TARGET_ADDRESS}")
    print(f"Hash160 alvo: {TARGET_HASH160.hex()}")
    print(f"Range: {START_KEY:064x} a {END_KEY:064x}")
    modo = "Aleatório" if RANDOM_SEARCH else "Sequencial"
    print(f"Modo: {modo}")
    print(f"Depuração: {'Ativada' if debug_mode else 'Desativada'}")
    
    # Verificar qual chave gera o endereço alvo
    print(f"🔍 VERIFICANDO DADOS:")
    for test_key in [1, 2, 3]:
        test_address = private_key_to_address(test_key)
        test_hash160 = private_key_to_hash160(test_key)
        print(f"Chave {test_key}: {test_address} -> {test_hash160.hex()}")

    if TARGET_ADDRESS == "1cMh228HTCiwS8ZsaakH8A8wze1JR5ZsP":
        print("❌ ERRO: Endereço 1cMh228HTCiwS8ZsaakH8A8wze1JR5ZsP não corresponde à chave 2!")
        print("✅ CORREÇÃO: Chave 2 gera **********************************")
    
    # Iniciar busca
    print(f"\n🚀 Iniciando busca personalizada...")
    found, key = search_keys(START_KEY, TARGET_HASH160)
    
    if found:
        print(f"\n🎉 SUCESSO! CHAVE ENCONTRADA!")
        print(f"🔑 Chave privada: {key:064x}")
        
        # Gerar informações completas usando CPU (lógica correta)
        address = private_key_to_address(key)
        wif = private_key_to_wif(key)
        hash160_cpu = private_key_to_hash160(key)
        
        print(f"📍 Endereço: {address}")
        print(f"🗝️  WIF: {wif}")
        print(f"🔍 Hash160 CPU (correto): {hash160_cpu.hex()}")
        
        # Verificar se é o endereço correto
        if address == TARGET_ADDRESS:
            print(f"✅ CONFIRMADO! Esta chave gera o endereço alvo!")
        else:
            print(f"❌ Erro: Endereço não confere")
    else:
        print(f"\n❌ Chave não encontrada no range especificado")

def verificar_dados_corretos():
    """Verifica se os dados estão corretos"""
    print("🔍 VERIFICANDO DADOS CORRETOS:")
    
    # Dados corretos conhecidos
    dados_corretos = {
        1: {
            "endereco": "**********************************",
            "hash160": "751e76e8199196d454941c45d1b3a323f1433bd6"
        },
        2: {
            "endereco": "1cMh228HTCiwS8ZsaakH8A8wze1JR5ZsP", 
            "hash160": "f54a5851e9372b87810a8e60cdd2e7cfd80b6e31"
        },
        3: {
            "endereco": "**********************************",
            "hash160": "7dd65592d0ab2fe0d0257d571abf032cd9db93dc"
        }
    }
    
    for chave, dados in dados_corretos.items():
        endereco_cpu = private_key_to_address(chave)
        hash160_cpu = private_key_to_hash160(chave)
        
        endereco_ok = endereco_cpu == dados["endereco"]
        hash160_ok = hash160_cpu.hex() == dados["hash160"]
        
        print(f"Chave {chave}:")
        print(f"  Endereço: {endereco_cpu} {'✅' if endereco_ok else '❌'}")
        print(f"  Hash160:  {hash160_cpu.hex()} {'✅' if hash160_ok else '❌'}")
        
        if not endereco_ok:
            print(f"    Esperado: {dados['endereco']}")
        if not hash160_ok:
            print(f"    Esperado: {dados['hash160']}")

def teste_rapido_validacao():
    """Teste rápido para verificar se a validação está funcionando"""
    print("🧪 TESTE RÁPIDO DE VALIDAÇÃO:")
    
    # Testar chave 3 que a GPU encontrou
    chave_encontrada = 3
    endereco_esperado = "**********************************"
    
    endereco_cpu = private_key_to_address(chave_encontrada)
    hash160_cpu = private_key_to_hash160(chave_encontrada)
    
    if endereco_cpu is None or hash160_cpu is None:
        print("❌ Erro ao gerar endereço ou hash160")
        return False
        
    print(f"Chave {chave_encontrada}:")
    print(f"  CPU address: {endereco_cpu}")
    print(f"  Expected:    {endereco_esperado}")
    print(f"  CPU hash160: {hash160_cpu.hex()}")
    
    if endereco_cpu == endereco_esperado:
        print("✅ VALIDAÇÃO CORRIGIDA! Chave 3 gera o endereço correto!")
        return True
    else:
        print("❌ Ainda há problema na validação")
        return False

if __name__ == "__main__":
    main()





